import { MigrationInterface, QueryRunner } from 'typeorm'

export class ResponseTime1748917708728 implements MigrationInterface {
  name = 'ResponseTime1748917708728'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" ADD "responseTime" integer`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "responseTime"`)
  }
}
