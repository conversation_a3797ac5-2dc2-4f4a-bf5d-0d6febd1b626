import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm'

@Entity('logs')
export class Log {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ type: 'uuid', nullable: false })
  user_id: string

  @Column({ type: 'text', nullable: false })
  username: string

  @Column({ type: 'text', nullable: false })
  action: string

  @Column({ type: 'text', nullable: false })
  target_type: string

  @Column({ type: 'uuid', nullable: true })
  target_id: string | null

  @Column({ type: 'text', nullable: false })
  target_name: string

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date
}
