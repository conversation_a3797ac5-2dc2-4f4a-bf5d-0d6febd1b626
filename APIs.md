# Danh sách tài kho<PERSON>n

## quyền admin

- `pentestadmin01/pentestadmin01`
- `pentestadmin02/pentestadmin02`

## quyền user

- `pentestuser01/pentestuser01`
- `pentestuser02/pentestuser02`

# Tài liệu API

## Tổng quan

**URL gốc**: `${baseURL}/api/v1`

**<PERSON><PERSON><PERSON> thực**:

- Bearer Token: `Authorization: Bearer ${accessToken}`
- Basic Auth: `Authorization: Basic ${btoa(username:password)}`

---

## 1. <PERSON><PERSON><PERSON> thực & Quản lý người dùng

### Xác thực người dùng

- `POST /user/login` - Đăng nhập người dùng

### Quản lý người dùng

- `GET /admin/{id}` - Lấy thông tin người dùng theo ID
- `GET /admin/group-users?search={search}` - L<PERSON>y tất cả người dùng nhóm
- `POST /admin/group-users/add` - Thêm người dùng nhóm
- `DELETE /admin/group-users/delete?idGroupname={idGroupname}` - Xóa người dùng nhóm
- `GET /admin/group-users/group?groupname={groupname}` - Lấy người dùng theo nhóm
- `GET /admin/group-users/all` - Lấy tất cả người dùng được nhóm theo tên nhóm
- `DELETE /admin/remove-user?id={id}` - Xóa người dùng
- `PATCH /admin/update-document` - Cập nhật tài liệu theo người dùng
- `GET /admin/group/{id}` - Lấy thông tin nhóm theo ID
- `PATCH /admin/update-group-user` - Cập nhật người dùng nhóm
- `GET /admin/users/get-all?limit={limit}&username={username}` - Lấy người dùng với phân trang

---

## 2. Luồng chat & Quy trình làm việc

### Quản lý luồng chat

- `GET /chatflows?type=MULTIAGENT&isPublic={isPublic}&page={page}&pageSize={pageSize}&searchQuery={searchQuery}` - Lấy tất cả luồng agent
- `GET /chatflows/{id}` - Lấy luồng chat cụ thể
- `POST /chatflows` - Tạo luồng chat mới
- `POST /chatflows/importchatflows` - Nhập luồng chat
- `PUT /chatflows/{id}` - Cập nhật luồng chat
- `DELETE /chatflows/{id}` - Xóa luồng chat
- `GET /chatflows/user/{userId}?type=MULTIAGENT&isPublic={isPublic}&page={page}&pageSize={pageSize}&searchQuery={searchQuery}` - Lấy luồng chat cá nhân

---

## 3. Tin nhắn chat

### Tin nhắn chat

- `POST /prediction/{chatflowid}` - Endpoint để trả lời tin nhắn trong giao diện chat

---

## 4. Nút & Thành phần

### Quản lý nút

- `GET /nodes` - Lấy tất cả các nút
- `GET /nodes/{name}` - Lấy nút cụ thể
- `GET /nodes/category/{name}` - Lấy nút theo danh mục
- `POST /node-config` - Lấy cấu hình nút

---

## 5. Thông tin xác thực

### Thông tin xác thực

- `GET /credentials?userId={userId}` - Lấy tất cả thông tin xác thực
- `GET /credentials/{id}` - Lấy thông tin xác thực cụ thể
- `POST /credentials` - Tạo thông tin xác thực
- `PUT /credentials/{id}` - Cập nhật thông tin xác thực
- `DELETE /credentials/{id}` - Xóa thông tin xác thực

---

## 6. Bảng điều khiển & Phân tích

### Bảng điều khiển

- `GET /dashboard/chat-history` - Lấy lịch sử phiên chat
- `GET /dashboard/customer-feedback` - Lấy phản hồi khách hàng
- `GET /dashboard/questions-over-time` - Lấy câu hỏi theo thời gian
- `GET /feedback/message-id/{id}` - Lấy câu trả lời theo ID tin nhắn

### Phản hồi

- `POST /feedback/{id}` - Thêm phản hồi
- `PUT /feedback/{id}` - Cập nhật phản hồi

---

## 7. Quản lý FAQ

### Thao tác FAQ

- `GET /faq?chatflowId={chatflowId}&limit={limit}&offset={offset}` - Lấy tất cả FAQ
- `GET /faq/search?chatflowId={chatflowId}` - Tìm kiếm FAQ
- `POST /faq` - Lưu FAQ
- `POST /faq/importfaqs` - Nhập FAQ
- `PUT /faq/{id}` - Cập nhật FAQ
- `DELETE /faq/delete/{id}/{chatflowId}` - Xóa FAQ
- `DELETE /faq/deleteall/{chatflowId}` - Xóa tất cả FAQ
- `DELETE /faq/deleteindex/{chatflowId}` - Xóa chỉ mục
- `POST /faq/settings/{chatflowId}` - Cập nhật cài đặt
- `GET /faq/list-classify-qa/{chatflowId}` - Lấy danh sách phân loại Q&A
- `POST /faq/label` - Tạo hoặc cập nhật nhãn
- `DELETE /faq/label/{id}/{chatflowId}` - Xóa nhãn

---

## 8. Nhập/Xuất

### Thao tác dữ liệu

- `POST /export-import/export` - Xuất dữ liệu
- `POST /export-import/import` - Nhập dữ liệu
- `POST /export-import/export-dashboard` - Xuất dashboard
- `POST /export-import/export-chat-history` - Xuất lịch sử chat

---

## 9. Quản lý kho tài liệu

Lưu ý phần này endpoint đổi sang `${baseURL}s3-explorer`

- `GET /api/files?cache=false&displayPrefixes=[]&prefix={prefix}%2F&excludePrivateFiles=true` - Lấy danh sách file theo prefix
- `POST /api/start-ingestion-job` - bắt đầu đồng bộ KB
- `POST /api/ingestion-job-status` - lấy trạng thái đồng bộ KB
- `GET /api/search-db?query={query}&rootPrefix={prefix}/&offset=0&limit=20` - tìm kiếm file
- `POST /api/create-folder` - tạo thư mục
- `DELETE /api/files` - xóa file hoặc thư mục
- `POST /api/upload` - tải lên file
