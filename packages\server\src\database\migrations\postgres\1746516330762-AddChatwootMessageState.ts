import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddChatwootMessageState1746516330762 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "chatwoot_message_state" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "chatwootConversationId" character varying(255) NOT NULL,
        "messageId" bigint NOT NULL,
        "createdDate" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_chatwoot_message_state" PRIMARY KEY ("id")
      )
    `)
    await queryRunner.query(
      `CREATE INDEX "IDX_chatwoot_message_state_conversation_id" ON "chatwoot_message_state" ("chatwootConversationId")`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "IDX_chatwoot_message_state_conversation_id"`)
    await queryRunner.query(`DROP TABLE "chatwoot_message_state"`)
  }
}
