import { StatusCodes } from 'http-status-codes'
import { ChatwootMessageState } from '../../database/entities/ChatwootMessageState'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getErrorMessage } from '../../errors/utils'

const getMessageIds = async (chatwootConversationId: string): Promise<number[]> => {
  try {
    const appServer = getRunningExpressApp()
    const messageStates = await appServer.AppDataSource.getRepository(ChatwootMessageState).find({
      where: { chatwootConversationId },
      select: ['messageId']
    })
    return messageStates.map((state) => state.messageId)
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatwootMessageStateService.getMessageIds - ${getErrorMessage(error)}`
    )
  }
}

const addMessageId = async (chatwootConversationId: string, messageId: number): Promise<void> => {
  try {
    const appServer = getRunningExpressApp()

    // Check if message ID already exists for this conversation
    const existingState = await appServer.AppDataSource.getRepository(ChatwootMessageState).findOne({
      where: { chatwootConversationId, messageId }
    })

    if (existingState) {
      return // Message ID already exists, no need to add again
    }

    const newMessageState = new ChatwootMessageState()
    newMessageState.chatwootConversationId = chatwootConversationId
    newMessageState.messageId = messageId

    await appServer.AppDataSource.getRepository(ChatwootMessageState).save(newMessageState)
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatwootMessageStateService.addMessageId - ${getErrorMessage(error)}`
    )
  }
}

const addMessageIds = async (chatwootConversationId: string, messageIds: number[]): Promise<void> => {
  try {
    const appServer = getRunningExpressApp()

    // Get existing message IDs for this conversation
    const existingMessageIds = await getMessageIds(chatwootConversationId)

    // Filter out message IDs that already exist
    const newMessageIds = messageIds.filter((id) => !existingMessageIds.includes(id))

    if (newMessageIds.length === 0) {
      return // No new message IDs to add
    }

    // Create new message state entities
    const newMessageStates = newMessageIds.map((messageId) => {
      const messageState = new ChatwootMessageState()
      messageState.chatwootConversationId = chatwootConversationId
      messageState.messageId = messageId
      return messageState
    })

    await appServer.AppDataSource.getRepository(ChatwootMessageState).save(newMessageStates)
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatwootMessageStateService.addMessageIds - ${getErrorMessage(error)}`
    )
  }
}

const isMessageIdExists = async (chatwootConversationId: string, messageId: number): Promise<boolean> => {
  try {
    const appServer = getRunningExpressApp()
    const messageState = await appServer.AppDataSource.getRepository(ChatwootMessageState).findOne({
      where: { chatwootConversationId, messageId }
    })
    return !!messageState
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatwootMessageStateService.isMessageIdExists - ${getErrorMessage(error)}`
    )
  }
}

export default {
  getMessageIds,
  addMessageId,
  addMessageIds,
  isMessageIdExists
}
