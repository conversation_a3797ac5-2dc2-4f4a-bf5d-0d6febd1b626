import axios from 'axios'
import express, { Router } from 'express'
import https from 'https'

const router: Router = express.Router()

router.get('/vib', async (req: any, res: any) => {
  try {
    const baseUrl = process.env.VIB_ITSM_API_BASE_URL
    const subscriptionKey = process.env.VIB_ITSM_API_SUBSCRIPTION_KEY

    if (!baseUrl || !subscriptionKey) {
      console.error(
        'Missing VIB ITSM API configuration. Please set VIB_ITSM_API_BASE_URL and VIB_ITSM_API_SUBSCRIPTION_KEY environment variables.'
      )
      return res.status(500).json({ error: 'VIB ITSM API configuration missing' })
    }

    const emails = req.query.emails
    let emailList: string[] = []

    if (Array.isArray(emails)) {
      emailList = emails as string[]
    } else if (typeof emails === 'string') {
      emailList = [emails]
    }

    const response = await axios.post(
      `${baseUrl}/api/integrate/list-user`,
      {
        emails: emailList
      },
      {
        headers: {
          Authorization: `Basic ${process.env.VIB_BASIC_AUTH_TOKEN}`,
          'Ocp-Apim-Subscription-Key': subscriptionKey
        },
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      }
    )

    res.status(200).json(response.data)
  } catch (err) {
    console.error('Error test api VIB', err)
    res.status(500).json(err)
  }
})

export default router
