import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTableChatflowFieldGroupNamesSharedWithUsers1747034375230 implements MigrationInterface {
  name = 'UpdateTableChatflowFieldGroupNamesSharedWithUsers1747034375230'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "groupNames" text DEFAULT '[]'`)
    await queryRunner.query(`ALTER TABLE "chat_flow" ADD "sharedWithUsers" text DEFAULT '[]'`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "sharedWithUsers"`)
    await queryRunner.query(`ALTER TABLE "chat_flow" DROP COLUMN "groupNames"`)
  }
}
