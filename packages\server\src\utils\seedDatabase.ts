import { DataSource } from 'typeorm'
import logger from './logger'

process.env.RUN_DATABASE_SEED = 'true'

let SEED_SQL = `
INSERT INTO public.group_users
(id, groupname, "createdDate", "updatedDate")
VALUES('1bb66647-6409-45bf-aff4-19682c3848b9'::uuid, 'Master_admin', '2025-02-07 01:36:15.224', '2025-02-07 01:36:15.224');
INSERT INTO public.users
(id, username, email, "role", "password", "createdDate", "updatedDate", "displayPrefixes", groupname, active)
VALUES('0299b2b0-980a-4d5c-8692-bc69e3b576e2'::uuid, 'cmc_ts_admin', NULL, 'MASTER_ADMIN'::public."users_role_enum", '$2a$10$9QUCWE4qMmWxmU5kncjDZOx2ApD3hiPcf6Sb2lY5hO3wcU6gdHv2S', '2024-12-26 07:30:47.222', '2024-12-26 07:30:47.222', NULL, 'Master_admin', true);
`

if (process.env.SEED_KNOWLEDGE_BASE_ID) {
  SEED_SQL += `
INSERT INTO public.knowledge_base
  (knowledge_base_id, data_source_ids, folder_name, index_name)
VALUES('${process.env.SEED_KNOWLEDGE_BASE_ID}', '${process.env.SEED_DATA_SOURCE_IDS}', '${process.env.SEED_FOLDER_NAME}', '${process.env.SEED_INDEX_NAME}');
`
}

/**
 * Check if database needs seeding by checking if users table is empty
 * @param {DataSource} appDataSource
 * @returns {Promise<boolean>}
 */
export const shouldRunSeeding = async (appDataSource: DataSource): Promise<boolean> => {
  try {
    const userRepository = appDataSource.getRepository('User')
    const userCount = await userRepository.count()
    return userCount === 0
  } catch (error) {
    logger.warn('Could not check if seeding is needed, skipping seeding:', error)
    return false
  }
}

/**
 * Transform SQL for different database types
 * @param {string} sql
 * @param {string} databaseType
 * @returns {string}
 */
const transformSqlForDatabase = (sql: string, databaseType: string): string => {
  let transformedSql = sql

  switch (databaseType) {
    case 'mysql':
    case 'mariadb':
      // Remove PostgreSQL-specific schema prefixes and casting
      transformedSql = transformedSql
        .replace(/public\./g, '')
        .replace(/::public\."[^"]+"/g, '')
        .replace(/::uuid/g, '')
        .replace(/::[a-zA-Z_"]+/g, '')
      break
    case 'sqlite':
      // Remove PostgreSQL-specific schema prefixes and casting
      transformedSql = transformedSql
        .replace(/public\./g, '')
        .replace(/::public\."[^"]+"/g, '')
        .replace(/::uuid/g, '')
        .replace(/::[a-zA-Z_"]+/g, '')
      break
    case 'postgres':
      // PostgreSQL syntax is already correct
      break
    default:
      logger.warn(`Unknown database type: ${databaseType}, using SQL as-is`)
      break
  }

  return transformedSql
}

/**
 * Execute SQL file for database seeding
 * @param {DataSource} appDataSource
 * @param {string} sqlFilePath
 */
export const executeSqlFile = async (appDataSource: DataSource): Promise<void> => {
  try {
    const sqlContent = SEED_SQL

    if (!sqlContent.trim()) {
      logger.warn('Seed file is empty, skipping seeding')
      return
    }

    const databaseType = appDataSource.options.type
    logger.info(`Preparing seed SQL for database type: ${databaseType}`)

    // Split SQL content by semicolons and filter out empty statements
    const sqlStatements = sqlContent
      .split(';')
      .map((statement) => statement.trim())
      .filter((statement) => statement.length > 0)
      .map((statement) => transformSqlForDatabase(statement, databaseType))

    if (sqlStatements.length === 0) {
      logger.warn('No valid SQL statements found in seed file')
      return
    }

    logger.info(`Executing ${sqlStatements.length} SQL statements from seed file`)

    // Execute each statement
    for (const statement of sqlStatements) {
      try {
        await appDataSource.query(statement)
        logger.debug(`Executed SQL statement: ${statement.substring(0, 100)}...`)
      } catch (error) {
        logger.error(`Failed to execute SQL statement: ${statement}`, error)
        throw error
      }
    }

    logger.info('Database seeding completed successfully')
  } catch (error) {
    logger.error('Error during database seeding:', error)
    throw error
  }
}

/**
 * Run database seeding if needed
 * @param {DataSource} appDataSource
 */
export const seedDatabase = async (appDataSource: DataSource): Promise<void> => {
  try {
    // Check if seeding should run (only for first-time deployment)
    const needsSeeding = await shouldRunSeeding(appDataSource)

    if (!needsSeeding) {
      logger.info('Database already contains data, skipping seeding')
      return
    }

    // Check if seeding is enabled via environment variable
    const runSeed = process.env.RUN_DATABASE_SEED === 'true'

    if (!runSeed) {
      logger.info('Database seeding is disabled (RUN_DATABASE_SEED not set to true)')
      return
    }

    logger.info('Starting database seeding for first-time deployment...')

    await executeSqlFile(appDataSource)
  } catch (error) {
    logger.error('Database seeding failed:', error)
    // Don't throw the error to prevent application startup failure
    // Seeding failure should not prevent the application from starting
  }
}
