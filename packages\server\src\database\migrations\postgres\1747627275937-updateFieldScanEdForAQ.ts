import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateFieldScanEdForAQ1747627275937 implements MigrationInterface {
  name = 'UpdateFieldScanEdForAQ1747627275937'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa" ADD "scanEd" boolean`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa" DROP COLUMN "scanEd"`)
  }
}
