# =============================================================================
# IMAGE
# =============================================================================
CAGENT_IMAGE=187091248012.dkr.ecr.us-east-1.amazonaws.com/prod/cagent:...
CAGENT_S3_EXPLORER_API_IMAGE=187091248012.dkr.ecr.us-east-1.amazonaws.com/prod/cagent:...
CAGENT_LIVECHAT_IMAGE=187091248012.dkr.ecr.us-east-1.amazonaws.com/prod/cagent:...
CAGENT_VIB_DATA_PROCESS_IMAGE=187091248012.dkr.ecr.us-east-1.amazonaws.com/prod/cagent:...

# =============================================================================
# PORT
# =============================================================================
CAGENT_PORT=3000
CAGENT_S3_EXPLORER_API_PORT=3002
CAGENT_LIVECHAT_PORT=3001
CAGENT_VIB_DATA_PROCESS_PORT=8000

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
CAGENT_MEMORY_LIMIT=8G
CAGENT_REPLICAS=2

# =============================================================================
# CORS & SECURITY CONFIGURATION
# =============================================================================
CAGENT_CORS_ORIGINS=*
CAGENT_IFRAME_ORIGINS=*

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
CAGENT_DATABASE_TYPE=postgres
CAGENT_DATABASE_PORT=5432
CAGENT_DATABASE_HOST=
CAGENT_DATABASE_NAME=c_agent
CAGENT_DATABASE_USER=c_agent
CAGENT_DATABASE_PASSWORD=

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
CAGENT_FLOWISE_SECRETKEY_OVERWRITE=myencryptionkey
CAGENT_FLOWISE_FILE_SIZE_LIMIT=256mb
CAGENT_STORAGE_TYPE=local
CAGENT_APIKEY_STORAGE_TYPE=db

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
CAGENT_LOGIN_TYPE=token
CAGENT_ACCESS_TOKEN_SECRET=
CAGENT_REFRESH_TOKEN_SECRET=

# =============================================================================
# DOCUMENT STORE CONFIGURATION
# =============================================================================
CAGENT_VITE_DOCUMENT_STORE_TYPE=s3
CAGENT_VITE_DOCUMENT_STORE_BASE_URL=

# =============================================================================
# SEARCH SERVICE CONFIGURATION
# =============================================================================
CAGENT_MEILISEARCH_HOST=
CAGENT_MEILISEARCH_API_KEY=

# =============================================================================
# EXTERNAL API CONFIGURATION
# =============================================================================
CAGENT_VIB_BASIC_AUTH_TOKEN=

# =============================================================================
# AWS S3 CONFIGURATION
# =============================================================================
CAGENT_S3_STORAGE_REGION=us-east-1
CAGENT_S3_STORAGE_BUCKET_NAME=cagent-poc-docs

# =============================================================================
# DATABASE SEEDING CONFIGURATION
# =============================================================================
CAGENT_SEED_KNOWLEDGE_BASE_ID=
CAGENT_SEED_DATA_SOURCE_IDS=
# Knowledge base folder name, example: vib-sample/
CAGENT_SEED_FOLDER_NAME=vib-sample/
CAGENT_SEED_INDEX_NAME=

# =============================================================================
# CHATWOOT INTEGRATION CONFIGURATION
# =============================================================================
CAGENT_CHATWOOT_ACCESS_KEY=
CAGENT_CHATWOOT_BASE_URL=
CAGENT_CHATWOOT_ACCOUNT_ID=

# REDIS CONFIGURATION FOR S3 EXPLORER
CAGENT_REDIS_HOST=
CAGENT_REDIS_PORT=6379
CAGENT_REDIS_PASSWORD=

# DATABASE CONFIGURATION FOR S3 EXPLORER
CAGENT_S3_EXPLORER_DATABASE_TYPE=postgres
CAGENT_S3_EXPLORER_DATABASE_HOST=
CAGENT_S3_EXPLORER_DATABASE_PORT=5432
CAGENT_S3_EXPLORER_DATABASE_NAME=postgres
CAGENT_S3_EXPLORER_DATABASE_USER=
CAGENT_S3_EXPLORER_DATABASE_PASSWORD=

# MEILISEARCH CONFIGURATION FOR S3 EXPLORER
CAGENT_S3_EXPLORER_MEILISEARCH_HOST=
CAGENT_S3_EXPLORER_MEILISEARCH_API_KEY=

# AWS BEDROCK CONFIGURATION FOR S3 EXPLORER
CAGENT_S3_EXPLORER_COLLECTION_ARN=
CAGENT_S3_EXPLORER_BUCKET_OWNER_ACCOUNT_ID=
CAGENT_S3_EXPLORER_ROLE_ARN=

# =============================================================================
# LIVECHAT SERVICE CONFIGURATION
# =============================================================================

# LIVECHAT APPLICATION CONFIGURATION
CAGENT_LIVECHAT_NODE_ENV=production
CAGENT_LIVECHAT_RAILS_ENV=production
CAGENT_LIVECHAT_FRONTEND_URL=http://localhost:8052
CAGENT_LIVECHAT_SECRET_KEY_BASE=

# LIVECHAT REDIS CONFIGURATION
CAGENT_LIVECHAT_REDIS_URL=
CAGENT_LIVECHAT_REDIS_PASSWORD=
CAGENT_LIVECHAT_REDIS_OPENSSL_VERIFY_MODE=none

# LIVECHAT DATABASE CONFIGURATION
CAGENT_LIVECHAT_POSTGRES_DATABASE=
CAGENT_LIVECHAT_POSTGRES_HOST=
CAGENT_LIVECHAT_POSTGRES_PORT=5432
CAGENT_LIVECHAT_POSTGRES_USERNAME=
CAGENT_LIVECHAT_POSTGRES_PASSWORD=

# =============================================================================
# VIB DATA PROCESS SERVICE CONFIGURATION
# =============================================================================

# AWS Configuration for Bedrock OCR
CAGENT_VIB_DATA_PROCESS_AWS_REGION=us-east-1

# Application Configuration
CAGENT_VIB_DATA_PROCESS_PYTHONUNBUFFERED=1
CAGENT_VIB_DATA_PROCESS_SEND_RESPONSE_API_URL=