#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const https = require('https')

/**
 * Script to download web.min.js from CDN using version from package.json
 * Usage: node scripts/update-popup.js
 */

const PACKAGE_JSON_PATH = path.join(__dirname, '..', 'packages', 'ui', 'package.json')
const OUTPUT_PATH = path.join(__dirname, '..', 'packages', 'server', 'public', 'web.min.js')
const PACKAGE_NAME = 'cmcts-c-agent-embedding'

/**
 * Read and parse package.json to extract version
 */
function getPackageVersion() {
  try {
    console.log(`Reading version from ${PACKAGE_JSON_PATH}`)

    if (!fs.existsSync(PACKAGE_JSON_PATH)) {
      throw new Error(`Package.json file not found at: ${PACKAGE_JSON_PATH}`)
    }

    const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, 'utf8'))
    const version = packageJson.dependencies?.[PACKAGE_NAME]

    if (!version) {
      throw new Error(`Package '${PACKAGE_NAME}' not found in dependencies`)
    }

    console.log(`Found version: ${version}`)
    return version
  } catch (error) {
    console.error(`Failed to read version from package.json: ${error.message}`)
    process.exit(1)
  }
}

/**
 * Download file from URL and save to output path
 */
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`Downloading from: ${url}`)
    console.log(`Saving to: ${outputPath}`)

    // Create directory if it doesn't exist
    const outputDir = path.dirname(outputPath)
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
      console.log(`Created directory: ${outputDir}`)
    }

    const file = fs.createWriteStream(outputPath)

    https
      .get(url, (response) => {
        // Check if request was successful
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`))
          return
        }

        // Track download progress
        const totalSize = parseInt(response.headers['content-length'] || '0')
        let downloadedSize = 0

        response.on('data', (chunk) => {
          downloadedSize += chunk.length
          if (totalSize > 0) {
            const progress = ((downloadedSize / totalSize) * 100).toFixed(1)
            process.stdout.write(`\rDownloading... ${progress}% (${downloadedSize}/${totalSize} bytes)`)
          }
        })

        response.pipe(file)

        file.on('finish', () => {
          file.close()
          console.log(`\nSuccessfully downloaded ${downloadedSize} bytes to ${outputPath}`)
          resolve()
        })

        file.on('error', (error) => {
          fs.unlink(outputPath, () => {}) // Delete partial file
          reject(error)
        })
      })
      .on('error', (error) => {
        reject(error)
      })
  })
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('Starting download script...')

    // Get version from package.json
    const version = getPackageVersion()

    // Construct CDN URL
    const cdnUrl = `https://cdn.jsdelivr.net/npm/${PACKAGE_NAME}@${version}/dist/web.min.js`

    // Download the file
    await downloadFile(cdnUrl, OUTPUT_PATH)

    console.log('Download completed successfully! ✅')
  } catch (error) {
    console.error(`Script failed: ${error.message}`)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { getPackageVersion, downloadFile, main }
