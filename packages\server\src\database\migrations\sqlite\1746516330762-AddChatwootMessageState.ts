import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddChatwootMessageState1746516330762 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "chatwoot_message_state" (
        "id" varchar PRIMARY KEY NOT NULL,
        "chatwootConversationId" varchar(255) NOT NULL,
        "messageId" bigint NOT NULL,
        "createdDate" datetime NOT NULL DEFAULT (datetime('now'))
      )
    `)
    await queryRunner.query(
      `CREATE INDEX "IDX_chatwoot_message_state_conversation_id" ON "chatwoot_message_state" ("chatwootConversationId")`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "IDX_chatwoot_message_state_conversation_id"`)
    await queryRunner.query(`DROP TABLE "chatwoot_message_state"`)
  }
}
