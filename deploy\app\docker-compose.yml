services:
  cagent:
    image: ${CAGENT_IMAGE}
    pull_policy: always
    restart: unless-stopped
    ports:
      - '${CAGENT_PORT:-3000}:3000'
    environment:
      # CORS & SECURITY CONFIGURATION
      - CORS_ORIGINS=${CAGENT_CORS_ORIGINS:-*}
      - IFRAME_ORIGINS=${CAGENT_IFRAME_ORIGINS:-*}

      # DATABASE CONFIGURATION
      - DATABASE_TYPE=${CAGENT_DATABASE_TYPE:-postgres}
      - DATABASE_PORT=${CAGENT_DATABASE_PORT}
      - DATABASE_HOST=${CAGENT_DATABASE_HOST}
      - DATABASE_NAME=${CAGENT_DATABASE_NAME}
      - DATABASE_USER=${CAGENT_DATABASE_USER}
      - DATABASE_PASSWORD=${CAGENT_DATABASE_PASSWORD}

      # APPLICATION CONFIGURATION
      - FLOWISE_SECRETKEY_OVERWRITE=${CAGENT_FLOWISE_SECRETKEY_OVERWRITE:-myencryptionkey}
      - FLOWISE_FILE_SIZE_LIMIT=${CAGENT_FLOWISE_FILE_SIZE_LIMIT:-256mb}
      - STORAGE_TYPE=${CAGENT_STORAGE_TYPE:-local}
      - APIKEY_STORAGE_TYPE=${CAGENT_APIKEY_STORAGE_TYPE:-db}

      # AUTHENTICATION CONFIGURATION
      - LOGIN_TYPE=${CAGENT_LOGIN_TYPE:-token}
      - ACCESS_TOKEN_SECRET=${CAGENT_ACCESS_TOKEN_SECRET}
      - REFRESH_TOKEN_SECRET=${CAGENT_REFRESH_TOKEN_SECRET}

      # DOCUMENT STORE CONFIGURATION
      - VITE_DOCUMENT_STORE_TYPE=${CAGENT_VITE_DOCUMENT_STORE_TYPE:-s3}
      - VITE_DOCUMENT_STORE_BASE_URL=${CAGENT_VITE_DOCUMENT_STORE_BASE_URL}

      # SEARCH SERVICE CONFIGURATION
      - MEILISEARCH_HOST=${CAGENT_MEILISEARCH_HOST}
      - MEILISEARCH_API_KEY=${CAGENT_MEILISEARCH_API_KEY}

      # EXTERNAL API CONFIGURATION
      - VIB_BASIC_AUTH_TOKEN=${CAGENT_VIB_BASIC_AUTH_TOKEN}

      # AWS S3 CONFIGURATION
      - S3_STORAGE_REGION=${CAGENT_S3_STORAGE_REGION:-us-east-1}
      - S3_STORAGE_BUCKET_NAME=${CAGENT_S3_STORAGE_BUCKET_NAME}

      # DATABASE SEEDING CONFIGURATION
      - SEED_KNOWLEDGE_BASE_ID=${CAGENT_SEED_KNOWLEDGE_BASE_ID}
      - SEED_DATA_SOURCE_IDS=${CAGENT_SEED_DATA_SOURCE_IDS}
      - SEED_FOLDER_NAME=${CAGENT_SEED_FOLDER_NAME}
      - SEED_INDEX_NAME=${CAGENT_SEED_INDEX_NAME}

      # CHATWOOT INTEGRATION CONFIGURATION
      - CHATWOOT_ACCESS_KEY=${CAGENT_CHATWOOT_ACCESS_KEY}
      - CHATWOOT_BASE_URL=${CAGENT_CHATWOOT_BASE_URL}
      - CHATWOOT_ACCOUNT_ID=${CAGENT_CHATWOOT_ACCOUNT_ID}

  s3-explorer-api:
    image: ${CAGENT_S3_EXPLORER_API_IMAGE}
    pull_policy: always
    restart: unless-stopped
    ports:
      - '${CAGENT_S3_EXPLORER_API_PORT:-3002}:3002'
    environment:
      # S3 STORAGE CONFIGURATION
      - S3_STORAGE_BUCKET_NAME=${CAGENT_S3_STORAGE_BUCKET_NAME}
      - S3_STORAGE_ACCESS_KEY_ID=${CAGENT_S3_STORAGE_ACCESS_KEY_ID}
      - S3_STORAGE_SECRET_ACCESS_KEY=${CAGENT_S3_STORAGE_SECRET_ACCESS_KEY}
      - S3_STORAGE_REGION=${CAGENT_S3_STORAGE_REGION:-us-east-1}

      # REDIS CONFIGURATION
      - REDIS_HOST=${CAGENT_REDIS_HOST}
      - REDIS_PORT=${CAGENT_REDIS_PORT:-6379}
      - REDIS_PASSWORD=${CAGENT_REDIS_PASSWORD}

      # DATABASE CONFIGURATION
      - DATABASE_TYPE=${CAGENT_S3_EXPLORER_DATABASE_TYPE:-postgres}
      - DATABASE_HOST=${CAGENT_S3_EXPLORER_DATABASE_HOST}
      - DATABASE_PORT=${CAGENT_S3_EXPLORER_DATABASE_PORT:-5432}
      - DATABASE_NAME=${CAGENT_S3_EXPLORER_DATABASE_NAME}
      - DATABASE_USER=${CAGENT_S3_EXPLORER_DATABASE_USER}
      - DATABASE_PASSWORD=${CAGENT_S3_EXPLORER_DATABASE_PASSWORD}

      # APPLICATION CONFIGURATION
      - PORT=${CAGENT_S3_EXPLORER_API_PORT:-3002}

      # MEILISEARCH CONFIGURATION
      - MEILISEARCH_HOST=${CAGENT_S3_EXPLORER_MEILISEARCH_HOST}
      - MEILISEARCH_API_KEY=${CAGENT_S3_EXPLORER_MEILISEARCH_API_KEY}

      # AWS BEDROCK CONFIGURATION
      - COLLECTION_ARN=${CAGENT_S3_EXPLORER_COLLECTION_ARN}
      - BUCKET_OWNER_ACCOUNT_ID=${CAGENT_S3_EXPLORER_BUCKET_OWNER_ACCOUNT_ID}
      - ROLE_ARN=${CAGENT_S3_EXPLORER_ROLE_ARN}

  cagent-livechat:
    image: ${CAGENT_LIVECHAT_IMAGE}
    pull_policy: always
    restart: unless-stopped
    ports:
      - '${CAGENT_LIVECHAT_PORT:-3001}:3000'
    command: bundle exec rails s -p 3000 -b 0.0.0.0
    environment:
      # LIVECHAT APPLICATION CONFIGURATION
      - NODE_ENV=${CAGENT_LIVECHAT_NODE_ENV:-production}
      - RAILS_ENV=${CAGENT_LIVECHAT_RAILS_ENV:-production}
      - FRONTEND_URL=${CAGENT_LIVECHAT_FRONTEND_URL}
      - SECRET_KEY_BASE=${CAGENT_LIVECHAT_SECRET_KEY_BASE}

      # LIVECHAT REDIS CONFIGURATION
      - REDIS_URL=${CAGENT_LIVECHAT_REDIS_URL}
      - REDIS_PASSWORD=${CAGENT_LIVECHAT_REDIS_PASSWORD}
      - REDIS_OPENSSL_VERIFY_MODE=${CAGENT_LIVECHAT_REDIS_OPENSSL_VERIFY_MODE:-none}

      # LIVECHAT DATABASE CONFIGURATION
      - POSTGRES_DATABASE=${CAGENT_LIVECHAT_POSTGRES_DATABASE}
      - POSTGRES_HOST=${CAGENT_LIVECHAT_POSTGRES_HOST}
      - POSTGRES_PORT=${CAGENT_LIVECHAT_POSTGRES_PORT:-5432}
      - POSTGRES_USERNAME=${CAGENT_LIVECHAT_POSTGRES_USERNAME}
      - POSTGRES_PASSWORD=${CAGENT_LIVECHAT_POSTGRES_PASSWORD}

  vib-data-process:
    image: ${CAGENT_VIB_DATA_PROCESS_IMAGE}
    pull_policy: always
    restart: unless-stopped
    ports:
      - '${CAGENT_VIB_DATA_PROCESS_PORT:-8000}:8000'
    environment:
      # AWS Configuration for Bedrock
      - AWS_REGION=${CAGENT_VIB_DATA_PROCESS_AWS_REGION:-us-east-1}
      # Application Configuration
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=${CAGENT_VIB_DATA_PROCESS_PYTHONUNBUFFERED:-1}
      - SEND_RESPONSE_API_URL=${CAGENT_VIB_DATA_PROCESS_SEND_RESPONSE_API_URL}
    volumes:
      # Mount temp directory for file processing
      - temp_data:/tmp/app
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/docs')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  temp_data:
    driver: local
