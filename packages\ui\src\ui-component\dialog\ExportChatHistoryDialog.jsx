import ExportingGIF from '@/assets/images/Exporting.gif'
import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, Grid, MenuItem, Stack, TextField } from '@mui/material'
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import PropTypes from 'prop-types'

const ExportChatHistoryDialog = ({ state, chatflow, onClose, onChange, onSubmit, loading }) => {
  return (
    <Dialog
      open={state.open}
      fullWidth
      maxWidth='xs'
      onClose={onClose}
      aria-labelledby='alert-dialog-title'
      aria-describedby='alert-dialog-description'
      disableRestoreFocus
    >
      {loading ? (
        <DialogContent>
          <Box sx={{ height: 'auto', display: 'flex', justifyContent: 'center', mb: 3 }}>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <img
                style={{
                  objectFit: 'cover',
                  height: 'auto',
                  width: 'auto'
                }}
                src={ExportingGIF}
                alt='ExportingGIF'
              />
              <span>Đang xuất báo cáo...</span>
            </div>
          </Box>
        </DialogContent>
      ) : (
        <>
          <DialogTitle sx={{ fontSize: '1rem' }} id='alert-dialog-title'>
            Xuất lịch sử chat
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} paddingTop={2}>
              <Grid item xs={12}>
                <Stack direction='row' spacing={2}>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      label='Từ ngày'
                      minDate={new Date(chatflow?.createdDate).getTime()}
                      maxDate={Date.now()}
                      inputFormat='DD/MM/YYYY'
                      value={state.startDate}
                      onChange={(value) => onChange('startDate', value)}
                      renderInput={(params) => <TextField size='small' sx={{ width: '50%' }} {...params} />}
                    />
                    <DatePicker
                      label='Đến ngày'
                      inputFormat='DD/MM/YYYY'
                      minDate={state.startDate || new Date(chatflow?.createdDate).getTime()}
                      maxDate={Date.now()}
                      value={state.endDate}
                      onChange={(value) => onChange('endDate', value)}
                      renderInput={(params) => <TextField size='small' sx={{ width: '50%' }} {...params} />}
                    />
                  </LocalizationProvider>
                </Stack>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={onClose}>Hủy</Button>
            <Button onClick={onSubmit} variant='contained' disabled={!state.startDate || !state.endDate}>
              Xuất
            </Button>
          </DialogActions>
        </>
      )}
    </Dialog>
  )
}

ExportChatHistoryDialog.propTypes = {
  show: PropTypes.bool.isRequired,
  chatflow: PropTypes.object,
  loading: PropTypes.bool.isRequired,
  state: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired
}

export default ExportChatHistoryDialog
