import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddChatwootMessageState1746516330762 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS \`chatwoot_message_state\` (
        \`id\` varchar(36) NOT NULL,
        \`chatwootConversationId\` varchar(255) NOT NULL,
        \`messageId\` bigint NOT NULL,
        \`createdDate\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        PRIMARY KEY (\`id\`),
        INDEX \`IDX_chatwoot_message_state_conversation_id\` (\`chatwootConversationId\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`chatwoot_message_state\``)
  }
}
