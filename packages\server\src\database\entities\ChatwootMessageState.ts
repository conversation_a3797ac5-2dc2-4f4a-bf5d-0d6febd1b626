import { <PERSON><PERSON><PERSON>, Column, CreateDateColumn, PrimaryGeneratedColumn, Index } from 'typeorm'

@Entity('chatwoot_message_state')
export class ChatwootMessageState {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ type: 'varchar', length: 255 })
  chatwootConversationId: string

  @Column({ type: 'bigint' })
  messageId: number

  @CreateDateColumn({ type: 'timestamp' })
  createdDate: Date
}
