import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTyperesponseTime1748920888022 implements MigrationInterface {
  name = 'UpdateTyperesponseTime1748920888022'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "responseTime"`)
    await queryRunner.query(`ALTER TABLE "chat_message" ADD "responseTime" double precision`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "chat_message" DROP COLUMN "responseTime"`)
    await queryRunner.query(`ALTER TABLE "chat_message" ADD "responseTime" integer`)
  }
}
