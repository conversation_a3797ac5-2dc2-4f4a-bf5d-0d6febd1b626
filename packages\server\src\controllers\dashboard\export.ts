import dayjs, { Dayjs } from 'dayjs'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { QA } from '../../database/entities/AQ'
import { ChatMessageFeedback } from '../../database/entities/ChatMessageFeedback'
import { ChatMessageRatingType } from '../../Interface'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import axios from 'axios'
import https from 'https'
import { User } from '../../database/entities/User'
import { In } from 'typeorm'
import { ChatMessage } from '../../database/entities/ChatMessage'
import { uniq } from 'lodash'

dayjs.extend(isSameOrBefore)

const exportByUserHeaders = [
  'STT',
  'Ngày',
  'Chi nhánh',
  'Người dùng',
  '<PERSON><PERSON> lượng câu hỏi',
  'Lượt like',
  'Lượt dislike',
  '<PERSON>hông đánh giá',
  '<PERSON>t không trả lời'
]
const exportByBranchHeaders = [
  'STT',
  'Ngày',
  '<PERSON> nhánh',
  'Tổng nhân sự đăng nhập',
  'Số lượng câu hỏi',
  'Lượt like',
  'Lượt dislike',
  'Không đánh giá',
  'Bot không trả lời'
]

function formatDate(date: Dayjs): string {
  return date.format('DD-MMM-YYYY')
}

function getDateRangeObject(startDate: Dayjs, endDate: Dayjs) {
  const dates: Record<string, any> = {}
  let currentDate = startDate.startOf('day')

  while (currentDate.isSameOrBefore(endDate, 'day')) {
    dates[formatDate(currentDate)] = {}
    currentDate = currentDate.add(1, 'day')
  }

  return dates
}

const getMappingBranchByUserEmail = async (emails: string[]) => {
  try {
    const baseUrl = process.env.VIB_ITSM_API_BASE_URL
    const subscriptionKey = process.env.VIB_ITSM_API_SUBSCRIPTION_KEY

    if (!baseUrl || !subscriptionKey) {
      console.error(
        'Missing VIB ITSM API configuration. Please set VIB_ITSM_API_BASE_URL and VIB_ITSM_API_SUBSCRIPTION_KEY environment variables.'
      )
      return {}
    }

    const response = await axios.post(
      `${baseUrl}/api/integrate/list-user`,
      {
        emails
      },
      {
        headers: {
          Authorization: `Basic ${process.env.VIB_BASIC_AUTH_TOKEN}`,
          'Ocp-Apim-Subscription-Key': subscriptionKey
        },
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      }
    )

    console.log('getMappingBranchByUserEmail', response)

    const result: any = {}
    ;(response.data?.OBJRETURN?.data || []).map((user: any) => {
      if (user?.email && user?.address) {
        result[user?.email] = { branch: user?.address, name: user?.fullname }
      }
    })

    return result
  } catch (err) {
    console.error('Error when getMappingBranchByUserEmail', err)
    return {}
  }
}

const getAllChatMessage = async (chatflowId: string, startDate: Dayjs, endDate: Dayjs) => {
  const appServer = getRunningExpressApp()
  const qaRepo = appServer.AppDataSource.getRepository(QA)

  const chatMessages = await qaRepo
    .createQueryBuilder('chatMessage')
    .where('chatMessage.chatflowid = :chatflowId', { chatflowId })
    .andWhere('chatMessage.createdDate BETWEEN :fromDate AND :toDate', {
      fromDate: startDate.toISOString(),
      toDate: endDate.toISOString()
    })
    .getMany()

  return chatMessages
}

const getAllFeedbacks = async (chatflowId: string, startDate: Dayjs, endDate: Dayjs) => {
  const appServer = getRunningExpressApp()
  const feedbackRepo = appServer.AppDataSource.getRepository(ChatMessageFeedback)

  const feedbacks = await feedbackRepo
    .createQueryBuilder('feedback')
    .where('feedback.chatflowid = :chatflowId', { chatflowId })
    .andWhere('feedback.createdDate BETWEEN :fromDate AND :toDate', {
      fromDate: startDate.toISOString(),
      toDate: endDate.toISOString()
    })
    .getMany()

  return feedbacks
}

const getAllUnAnsweredQuestions = async (chatflowId: string, startDate: Dayjs, endDate: Dayjs) => {
  const appServer = getRunningExpressApp()
  const qaRepo = appServer.AppDataSource.getRepository(QA)

  const unAnsweredQuestions = await qaRepo
    .createQueryBuilder('chatMessage')
    .where('chatMessage.chatflowid = :chatflowId AND chatMessage.being_processed = false', { chatflowId })
    .andWhere('chatMessage.createdDate BETWEEN :fromDate AND :toDate', {
      fromDate: startDate.toISOString(),
      toDate: endDate.toISOString()
    })
    .getMany()

  return unAnsweredQuestions
}

const getAllUsers = async (userIds: string[]) => {
  return getRunningExpressApp()
    .AppDataSource.getRepository(User)
    .find({
      where: {
        id: In(userIds)
      }
    })
}

export const getChatMessageByChatMessageID = async (chatMessageID: string) => {
  return getRunningExpressApp()
    .AppDataSource.getRepository(ChatMessage)
    .findOne({
      where: {
        id: chatMessageID
      }
    })
}

const ensureResultUserEntry = (result: any, date: string, branch: string, user: User, mappingBranch: any) => {
  if (!result[date][branch]) result[date][branch] = {}

  if (!result[date][branch][user.id]) {
    result[date][branch][user.id] = {
      user: mappingBranch[user.email]?.name || user.username
    }
  }

  return result[date][branch][user.id]
}

const reportCountByDateRange = async (
  startDate: Dayjs,
  endDate: Dayjs,
  chatMessages: QA[],
  feedbacks: ChatMessageFeedback[],
  unAnsweredQuestions: QA[],
  mappingBranch: any,
  users: User[]
) => {
  const defaultUser = {
    id: '',
    email: '',
    username: 'undefined'
  }
  const getUserMappingById = getMappingUserById(users)

  console.log('mappingBranch', mappingBranch)

  const result = getDateRangeObject(startDate, endDate)

  chatMessages.forEach((chatMessage) => {
    const date = formatDate(dayjs(chatMessage.createdDate))
    const user = getUserMappingById[chatMessage.userId] || defaultUser

    if (result[date]) {
      const branch = mappingBranch[user.email]?.branch || 'NONAME'
      const entry = ensureResultUserEntry(result, date, branch, user, mappingBranch)
      entry.messageCount = (entry.messageCount || 0) + 1
    }
  })

  for (const feedback of feedbacks) {
    const date = formatDate(dayjs(feedback.createdDate))
    const message = await getChatMessageByChatMessageID(feedback.messageId)
    const user = getUserMappingById[message?.userId || ''] || defaultUser

    if (result[date]) {
      const branch = mappingBranch[user.email]?.branch || 'NONAME'
      if (result[date]?.[branch]?.[user.id]?.messageCount) {
        const entry = ensureResultUserEntry(result, date, branch, user, mappingBranch)

        if (!entry.rating) {
          entry.rating = { positive: 0, negative: 0 }
        }
        const key = feedback.rating === ChatMessageRatingType.THUMBS_UP ? 'positive' : 'negative'
        entry.rating[key]++
      }
    }
  }

  unAnsweredQuestions.forEach((chatMessage) => {
    const date = formatDate(dayjs(chatMessage.createdDate))
    const user = getUserMappingById[chatMessage.userId] || defaultUser

    if (result[date]) {
      const branch = mappingBranch[user.email]?.branch || 'NONAME'
      const entry = ensureResultUserEntry(result, date, branch, user, mappingBranch)
      entry.unAnsweredQuestions = (entry.unAnsweredQuestions || 0) + 1
    }
  })

  return result
}

function arrayToCSV(data: any[], headers: string[], requireSummary = false): string {
  if (data.length === 0) return ''
  const head = headers.join(',')

  let sum = Array(headers.length).fill(0)

  const dataWithIndex = data.map((row, i) => (Array.isArray(row) ? [i + 1, ...row] : { stt: i + 1, ...row }))
  const rows = dataWithIndex.map((row) =>
    Object.values(row)
      .map((val, index) => {
        if (typeof val === 'number' && index !== 0) {
          sum[index] += val
        }
        return `"${String(val ?? '').replace(/"/g, '""')}"`
      })
      .join(',')
  )

  sum = sum.map((num) => num || '')
  sum[2] = 'Tổng'

  return [head, ...rows, requireSummary ? sum.join(',') : ''].join('\n')
}

type FlattenedBranchStat = {
  date: string
  branch: string
  userCount: number
  messageCount: number
  ratingPositive: number
  ratingNegative: number
  ratingNone: number
  unAnsweredQuestions: number
}

function exportByBranchToCSV(data: Record<string, any>): string {
  const rows: FlattenedBranchStat[] = []

  for (const date in data) {
    for (const branch in data[date]) {
      let messageCount = 0
      let unAnsweredQuestions = 0
      let ratingPositive = 0
      let ratingNegative = 0

      for (const userId in data[date][branch]) {
        const userData = data[date][branch][userId]
        messageCount += userData.messageCount || 0
        unAnsweredQuestions += userData.unAnsweredQuestions || 0
        ratingPositive += userData.rating?.positive || 0
        ratingNegative += userData.rating?.negative || 0
      }

      rows.push({
        date,
        branch,
        userCount: Object.keys(data[date][branch]).length,
        messageCount,
        ratingPositive,
        ratingNegative,
        ratingNone: messageCount - (ratingPositive + ratingNegative),
        unAnsweredQuestions
      })
    }
  }

  return arrayToCSV(rows, exportByBranchHeaders, true)
}

type FlattenedUserStat = {
  date: string
  branch: string
  user: string
  messageCount: number
  ratingPositive: number
  ratingNegative: number
  ratingNone: number
  unAnsweredQuestions: number
}

function exportByUserToCSV(data: Record<string, any>): string {
  const rows: FlattenedUserStat[] = []

  for (const date in data) {
    for (const branch in data[date]) {
      for (const userId in data[date][branch]) {
        const userData = data[date][branch][userId]
        rows.push({
          date,
          branch,
          user: userData.user,
          messageCount: userData.messageCount || 0,
          ratingPositive: userData.rating?.positive || 0,
          ratingNegative: userData.rating?.negative || 0,
          ratingNone: (userData.messageCount || 0) - ((userData.rating?.positive || 0) + (userData.rating?.negative || 0)),
          unAnsweredQuestions: userData.unAnsweredQuestions || 0
        })
      }
    }
  }

  return arrayToCSV(rows, exportByUserHeaders, true)
}

const getMappingUserById = (users: User[]) => {
  return users.reduce((acc: { [key: string]: User }, item) => {
    acc[item.id] = item
    return acc
  }, {})
}

export const handleExportDashboard = async (chatflowId: string, startDate: Date, endDate: Date, type: 'full' | 'branch') => {
  try {
    const fromDate = dayjs(startDate).add(1, 'day').startOf('day')
    const toDate = dayjs(endDate).add(1, 'day').endOf('day')

    const [chatMessages, feedbacks, unAnsweredQuestions] = await Promise.all([
      getAllChatMessage(chatflowId, fromDate, toDate),
      getAllFeedbacks(chatflowId, fromDate, toDate),
      getAllUnAnsweredQuestions(chatflowId, fromDate, toDate)
    ])

    const users = await getAllUsers(uniq(chatMessages.map(({ userId }) => userId)).filter(Boolean))
    const mappingBranch = await getMappingBranchByUserEmail(users.map(({ email }) => email).filter(Boolean))

    const data = await reportCountByDateRange(fromDate, toDate, chatMessages, feedbacks, unAnsweredQuestions, mappingBranch, users)
    return type === 'full' ? exportByBranchToCSV(data) : exportByUserToCSV(data)
  } catch (err) {
    console.log('error', err)
    return ''
  }
}

const exportChatHisotry = (chatMessages: QA[], feedbacks: ChatMessageFeedback[]) => {
  const result: any[] = []
  chatMessages
    .filter((message) => message.answer && message.userName)
    .forEach((message) => {
      const { id, question, answer, userName, createdDate } = message
      const rating = feedbacks.find(
        (fb) => fb.chatId === message.chatId && fb.question === message.question && fb.answer?.trim() === message.answer?.trim()
      )
      const r = rating?.rating ? (rating.rating === ChatMessageRatingType.THUMBS_UP ? 'Tích cực' : 'Tiêu cực') : ''
      const rt = rating?.content || ''
      result.push([`${id}`, dayjs(createdDate).format('YYYY/MM/DD HH:mm:ss'), question, answer, r, rt, userName])
    })

  return arrayToCSV(result, ['STT', 'ID', 'Thời gian', 'Câu hỏi', 'Trả lời', 'Đánh giá', 'Nội dung đánh giá', 'Người hỏi'])
}

export const handleExportChatHistory = async (chatflowId: string, startDate: Date, endDate: Date) => {
  try {
    const fromDate = dayjs(startDate).add(1, 'day').startOf('day')
    const toDate = dayjs(endDate).add(1, 'day').endOf('day')

    const appServer = getRunningExpressApp()
    const chatMessageRepo = appServer.AppDataSource.getRepository(QA)
    const feedbackRepo = appServer.AppDataSource.getRepository(ChatMessageFeedback)

    const qaList = await chatMessageRepo
      .createQueryBuilder('qa')
      .where('qa.chatflowid = :chatflowId', { chatflowId })
      .andWhere('qa.createdDate BETWEEN :fromDate AND :toDate', {
        fromDate,
        toDate
      })
      .orderBy('qa.createdDate', 'DESC')
      .getMany()

    const chatIds = qaList.map((qa) => qa.chatId)
    const feedbacks = await feedbackRepo.createQueryBuilder('feedback').where('feedback.chatId IN (:...chatIds)', { chatIds }).getMany()

    return exportChatHisotry(qaList, feedbacks)
  } catch (err) {
    console.log('error', err)
    return ''
  }
}
