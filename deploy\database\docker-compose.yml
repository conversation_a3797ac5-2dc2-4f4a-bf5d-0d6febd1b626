# C-Agent Database Services

services:
  postgres-main:
    image: postgres:16-alpine
    restart: unless-stopped

    command: >
      postgres
      -c listen_addresses='*'
      -c timezone=UTC

    environment:
      POSTGRES_DB: ${POSTGRES_MAIN_DB:-c_agent}
      POSTGRES_USER: ${POSTGRES_MAIN_USER:-c_agent}
      POSTGRES_PASSWORD: ${POSTGRES_MAIN_PASSWORD:-c_agent_password}
      POSTGRES_INITDB_ARGS: '--encoding=UTF-8 --lc-collate=C --lc-ctype=C'

    volumes:
      - postgres_main_data:/var/lib/postgresql/data

    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_MAIN_USER:-c_agent} -d ${POSTGRES_MAIN_DB:-c_agent}']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  postgres-memories:
    image: postgres:16-alpine
    restart: unless-stopped

    command: >
      postgres
      -c listen_addresses='*'
      -c timezone=UTC

    environment:
      POSTGRES_DB: ${POSTGRES_MEMORIES_DB:-c_agent_memories}
      POSTGRES_USER: ${POSTGRES_MEMORIES_USER:-memories}
      POSTGRES_PASSWORD: ${POSTGRES_MEMORIES_PASSWORD:-memories_password}
      POSTGRES_INITDB_ARGS: '--encoding=UTF-8 --lc-collate=C --lc-ctype=C'

    volumes:
      - postgres_memories_data:/var/lib/postgresql/data

    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_MEMORIES_USER:-memories} -d ${POSTGRES_MEMORIES_DB:-c_agent_memories}']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  pgbouncer-main:
    image: edoburu/pgbouncer:latest
    restart: unless-stopped
    depends_on:
      postgres-main:
        condition: service_healthy

    environment:
      DB_HOST: postgres-main
      DB_USER: ${POSTGRES_MAIN_USER:-c_agent}
      DB_PASSWORD: ${POSTGRES_MAIN_PASSWORD:-c_agent_password}
      DB_NAME: ${POSTGRES_MAIN_DB:-c_agent}
      POOL_MODE: ${PGBOUNCER_MAIN_POOL_MODE:-transaction}
      MAX_CLIENT_CONN: ${PGBOUNCER_MAIN_MAX_CLIENT_CONN:-10000}
      DEFAULT_POOL_SIZE: ${PGBOUNCER_MAIN_DEFAULT_POOL_SIZE:-20}
      AUTH_TYPE: ${PGBOUNCER_MAIN_AUTH_TYPE:-scram-sha-256}

    ports:
      - '${POSTGRES_MAIN_PORT:-5432}:5432'

    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -p 5432']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

    logging:
      driver: 'json-file'
      options:
        max-size: '5m'
        max-file: '3'

  pgbouncer-memories:
    image: edoburu/pgbouncer:latest
    restart: unless-stopped
    depends_on:
      postgres-memories:
        condition: service_healthy

    environment:
      DB_HOST: postgres-memories
      DB_USER: ${POSTGRES_MEMORIES_USER:-memories}
      DB_PASSWORD: ${POSTGRES_MEMORIES_PASSWORD:-memories_password}
      DB_NAME: ${POSTGRES_MEMORIES_DB:-c_agent_memories}
      POOL_MODE: ${PGBOUNCER_MEMORIES_POOL_MODE:-transaction}
      MAX_CLIENT_CONN: ${PGBOUNCER_MEMORIES_MAX_CLIENT_CONN:-10000}
      DEFAULT_POOL_SIZE: ${PGBOUNCER_MEMORIES_DEFAULT_POOL_SIZE:-20}
      AUTH_TYPE: ${PGBOUNCER_MEMORIES_AUTH_TYPE:-scram-sha-256}

    ports:
      - '${POSTGRES_MEMORIES_PORT:-5433}:5432'

    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -p 5432']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

    logging:
      driver: 'json-file'
      options:
        max-size: '5m'
        max-file: '3'

  postgres-livechat:
    image: pgvector/pgvector:pg17
    restart: unless-stopped

    command: >
      postgres
      -c listen_addresses='*'
      -c timezone=UTC

    environment:
      POSTGRES_DB: ${POSTGRES_LIVECHAT_DB:-c_agent_livechat}
      POSTGRES_USER: ${POSTGRES_LIVECHAT_USER:-livechat}
      POSTGRES_PASSWORD: ${POSTGRES_LIVECHAT_PASSWORD:-livechat_password}
      POSTGRES_INITDB_ARGS: '--encoding=UTF-8 --lc-collate=C --lc-ctype=C'

    ports:
      - '${POSTGRES_LIVECHAT_PORT:-5434}:5432'

    volumes:
      - postgres_livechat_data:/var/lib/postgresql/data

    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_LIVECHAT_USER:-livechat} -d ${POSTGRES_LIVECHAT_DB:-c_agent_livechat}']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  redis:
    image: redis:7-alpine
    restart: unless-stopped

    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-redis_password}
      --appendonly yes

    ports:
      - '${REDIS_PORT:-6379}:6379'

    volumes:
      - redis_data:/data

    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

    logging:
      driver: 'json-file'
      options:
        max-size: '5m'
        max-file: '3'

  redis-livechat:
    image: redis:7-alpine
    restart: unless-stopped

    command: >
      redis-server
      --requirepass ${REDIS_LIVECHAT_PASSWORD:-redis_livechat_password}
      --appendonly yes

    ports:
      - '${REDIS_LIVECHAT_PORT:-6380}:6379'

    volumes:
      - redis_livechat_data:/data

    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

    logging:
      driver: 'json-file'
      options:
        max-size: '5m'
        max-file: '3'

  meilisearch:
    image: getmeili/meilisearch:v1.13.0
    restart: unless-stopped

    environment:
      MEILI_MASTER_KEY: ${MEILISEARCH_MASTER_KEY:-6503e9ce62dfbdd119ebc9819821a85948eeffcfe3b502c35ab1765c225b9aae}
      MEILI_NO_ANALYTICS: ${MEILISEARCH_NO_ANALYTICS:-false}
      MEILI_ENV: ${MEILISEARCH_ENV:-development}
      MEILI_HTTP_PAYLOAD_SIZE_LIMIT: ${MEILISEARCH_HTTP_PAYLOAD_SIZE_LIMIT:-104857600}
      MEILI_MAX_MDB_SIZE: ${MEILISEARCH_MAX_MDB_SIZE:-107374182400}
      MEILI_MAX_UDB_SIZE: ${MEILISEARCH_MAX_UDB_SIZE:-107374182400}
      MEILI_NO_SENTRY: ${MEILISEARCH_NO_SENTRY:-true}
      MEILI_SCHEDULE_SNAPSHOT: ${MEILISEARCH_SCHEDULE_SNAPSHOT:-}
      MEILI_SNAPSHOT_INTERVAL_SEC: ${MEILISEARCH_SNAPSHOT_INTERVAL_SEC:-}
      MEILI_DUMP_BATCH_SIZE: ${MEILISEARCH_DUMP_BATCH_SIZE:-1024}
      MEILI_HTTP_ADDR: ${MEILISEARCH_HTTP_ADDR:-0.0.0.0:7700}

    ports:
      - '${MEILISEARCH_PORT:-7700}:7700'

    volumes:
      - meilisearch_data_ms:/data.ms
      - meilisearch_snapshot:/snapshot
      - meilisearch_dumps:/dumps
      - meilisearch_data:/meili_data

    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:7700/health']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

volumes:
  postgres_main_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/postgres-main

  postgres_memories_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/postgres-memories

  postgres_livechat_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/postgres-livechat

  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/redis

  redis_livechat_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/redis-livechat

  meilisearch_data_ms:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/meilisearch/data.ms

  meilisearch_snapshot:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/meilisearch/snapshot

  meilisearch_dumps:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/meilisearch/dumps

  meilisearch_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/meilisearch/meili_data
