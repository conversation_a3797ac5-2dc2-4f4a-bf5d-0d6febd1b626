# C-Agent Database Services Environment Configuration
# Copy this file to .env and customize the values for your environment

# =============================================================================
# DATA PERSISTENCE SETTINGS
# =============================================================================
# Base path for data storage (will create subdirectories for each service)
DATA_PATH=./data

# =============================================================================
# MAIN POSTGRESQL DATABASE SETTINGS
# =============================================================================
# Primary database for C-Agent application data
POSTGRES_MAIN_DB=c_agent
POSTGRES_MAIN_USER=c_agent
POSTGRES_MAIN_PASSWORD=RANDOM_PASSWORD_CHANGE_IN_PRODUCTION
POSTGRES_MAIN_PORT=5432

# =============================================================================
# MEMORIES POSTGRESQL DATABASE SETTINGS
# =============================================================================
# Secondary database for user chat history and conversation memories
POSTGRES_MEMORIES_DB=c_agent_memories
POSTGRES_MEMORIES_USER=memories
POSTGRES_MEMORIES_PASSWORD=RANDOM_PASSWORD_CHANGE_IN_PRODUCTION
POSTGRES_MEMORIES_PORT=5433

# =============================================================================
# LIVECHAT POSTGRESQL DATABASE SETTINGS
# =============================================================================
# Tertiary database for livechat functionality and data
POSTGRES_LIVECHAT_DB=c_agent_livechat
POSTGRES_LIVECHAT_USER=livechat
POSTGRES_LIVECHAT_PASSWORD=RANDOM_PASSWORD_CHANGE_IN_PRODUCTION
POSTGRES_LIVECHAT_PORT=5434

# =============================================================================
# REDIS CACHE SETTINGS
# =============================================================================
# Redis for caching, sessions, and real-time features
REDIS_PASSWORD=RANDOM_PASSWORD_CHANGE_IN_PRODUCTION
REDIS_PORT=6379

# =============================================================================
# REDIS LIVECHAT SETTINGS
# =============================================================================
# Redis for livechat real-time messaging, sessions, and caching
REDIS_LIVECHAT_PASSWORD=RANDOM_PASSWORD_CHANGE_IN_PRODUCTION
REDIS_LIVECHAT_PORT=6380

# =============================================================================
# PGBOUNCER MAIN DATABASE SETTINGS
# =============================================================================
# Connection pooling configuration for main database
PGBOUNCER_MAIN_POOL_MODE=transaction
PGBOUNCER_MAIN_MAX_CLIENT_CONN=300
PGBOUNCER_MAIN_DEFAULT_POOL_SIZE=30
PGBOUNCER_MAIN_AUTH_TYPE=scram-sha-256

# =============================================================================
# PGBOUNCER MEMORIES DATABASE SETTINGS
# =============================================================================
# Connection pooling configuration for memories database
PGBOUNCER_MEMORIES_POOL_MODE=transaction
PGBOUNCER_MEMORIES_MAX_CLIENT_CONN=500
PGBOUNCER_MEMORIES_DEFAULT_POOL_SIZE=50
PGBOUNCER_MEMORIES_AUTH_TYPE=scram-sha-256

# =============================================================================
# MEILISEARCH SETTINGS
# =============================================================================
# Search engine for full-text search capabilities
MEILISEARCH_MASTER_KEY=RANDOM_MASTER_KEY_CHANGE_IN_PRODUCTION
MEILISEARCH_NO_ANALYTICS=false
MEILISEARCH_ENV=production
MEILISEARCH_HTTP_PAYLOAD_SIZE_LIMIT=104857600
MEILISEARCH_MAX_MDB_SIZE=107374182400
MEILISEARCH_MAX_UDB_SIZE=107374182400
MEILISEARCH_NO_SENTRY=true
MEILISEARCH_SCHEDULE_SNAPSHOT=
MEILISEARCH_SNAPSHOT_INTERVAL_SEC=
MEILISEARCH_DUMP_BATCH_SIZE=1024
MEILISEARCH_HTTP_ADDR=0.0.0.0:7700
MEILISEARCH_PORT=7700